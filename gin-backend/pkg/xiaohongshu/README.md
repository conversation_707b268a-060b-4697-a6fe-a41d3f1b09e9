# 小红书广告API Go SDK

这是一个用于小红书广告API的Go语言SDK，主要实现了OAuth2认证相关的接口。

## 功能特性

- ✅ 获取访问令牌 (access_token)
- ✅ 刷新访问令牌 (refresh_token)
- ✅ 创意层级离线报表数据 (creative report)
- ✅ 支持生产环境和测试环境
- ✅ 完整的错误处理
- ✅ 类型安全的API响应
- ✅ 报表构建器和便利方法
- ✅ 分页、过滤、排序支持

## 安装

```bash
go get your-module/pkg/xiaohongshu
```

## 快速开始

### 1. 创建客户端

```go
package main

import (
    "fmt"
    "log"
    "your-module/pkg/xiaohongshu"
)

func main() {
    // 创建配置
    config := &xiaohongshu.Config{
        AppId:  "your_app_id",
        Secret: "your_secret", 
        IsProd: false, // 测试环境，生产环境设置为true
    }

    // 创建客户端
    client := xiaohongshu.NewClient(config)
}
```

### 2. 获取访问令牌

```go
// 使用授权码获取访问令牌
authCode := "d6a0b18531a2b9599a2c1e2361659c00"
tokenResp, err := client.GetAccessToken(authCode)
if err != nil {
    log.Printf("获取访问令牌失败: %v", err)
    return
}

if tokenResp.Success {
    fmt.Printf("访问令牌: %s\n", tokenResp.Data.AccessToken)
    fmt.Printf("刷新令牌: %s\n", tokenResp.Data.RefreshToken)
    fmt.Printf("广告主ID: %d\n", tokenResp.Data.AdvertiserId)
    fmt.Printf("访问令牌过期时间: %d秒\n", tokenResp.Data.AccessTokenExpiresIn)
}
```

### 3. 刷新访问令牌

```go
// 使用刷新令牌获取新的访问令牌
refreshToken := "5be1789576f45f90ccfbf4ba16ca4a5b"
newTokenResp, err := client.RefreshToken(refreshToken)
if err != nil {
    log.Printf("刷新访问令牌失败: %v", err)
    return
}

if newTokenResp.Success {
    fmt.Printf("新的访问令牌: %s\n", newTokenResp.Data.AccessToken)
    fmt.Printf("新的刷新令牌: %s\n", newTokenResp.Data.RefreshToken)
}
```

### 4. 获取创意报表

```go
// 使用构建器创建报表请求
request := xiaohongshu.NewReportBuilder(1234567890, "2024-01-01", "2024-01-07").
    WithTimeUnit(xiaohongshu.TimeUnitDay).
    WithPagination(1, 50).
    WithSort("fee", xiaohongshu.SortDesc).
    Build()

// 获取报表数据
accessToken := "your_access_token"
reportResp, err := client.GetCreativeReport(accessToken, request)
if err != nil {
    log.Printf("获取创意报表失败: %v", err)
    return
}

if reportResp.Success {
    fmt.Printf("总记录数: %d\n", reportResp.Data.TotalCount)
    fmt.Printf("数据列表长度: %d\n", len(reportResp.Data.DataList))

    // 处理报表数据
    for _, data := range reportResp.Data.DataList {
        fmt.Printf("计划: %s, 消费: %s, 展现: %s\n",
            data.CampaignName, data.Fee, data.Impression)
    }
}
```

### 5. 使用快速方法

```go
// 获取昨日报表
yesterdayRequest := xiaohongshu.CreateYesterdayReport(1234567890)
yesterdayResp, err := client.GetCreativeReport(accessToken, yesterdayRequest)

// 获取最近7天报表
last7DaysRequest := xiaohongshu.CreateLast7DaysReport(1234567890)
last7DaysResp, err := client.GetCreativeReport(accessToken, last7DaysRequest)

// 获取最近30天报表
last30DaysRequest := xiaohongshu.CreateLast30DaysReport(1234567890)
last30DaysResp, err := client.GetCreativeReport(accessToken, last30DaysRequest)
```

### 6. 使用过滤条件

```go
// 创建过滤条件
filters := []xiaohongshu.Filter{
    xiaohongshu.CreateFilterByFee(xiaohongshu.FilterOperatorGreaterThan, []string{"100"}),
    xiaohongshu.CreateFilterByClick(xiaohongshu.FilterOperatorGreaterThan, []string{"10"}),
    xiaohongshu.CreateFilterByCreativityID([]string{"123456", "789012"}),
}

// 构建带过滤条件的请求
request := xiaohongshu.NewReportBuilder(1234567890, "2024-01-01", "2024-01-31").
    WithTimeUnit(xiaohongshu.TimeUnitSummary).
    WithFilters(filters).
    WithSplitColumns(xiaohongshu.SplitColumnsCreativity).
    Build()
```

## API响应结构

### TokenResponse

```go
type TokenResponse struct {
    Code    int        `json:"code"`    // 状态码，0表示成功
    Success bool       `json:"success"` // 是否成功
    Msg     string     `json:"msg"`     // 消息
    Data    *TokenData `json:"data"`    // 令牌数据
}
```

### TokenData

```go
type TokenData struct {
    UserId                   string       `json:"user_id"`                     // 用户ID
    RoleType                 int          `json:"role_type"`                   // 角色类型
    ApprovalAdvertisers      []Advertiser `json:"approval_advertisers"`        // 授权的广告主列表
    RefreshToken             string       `json:"refresh_token"`               // 刷新令牌
    AdvertiserId             int64        `json:"advertiser_id"`               // 广告主ID
    RefreshTokenExpiresIn    int64        `json:"refresh_token_expires_in"`    // 刷新令牌过期时间(秒)
    ApprovalRoleType         int          `json:"approval_role_type"`          // 授权角色类型
    PlatformType             int          `json:"platform_type"`               // 平台类型
    AccessToken              string       `json:"access_token"`                // 访问令牌
    AccessTokenExpiresIn     int64        `json:"access_token_expires_in"`     // 访问令牌过期时间(秒)
}
```

## 错误处理

SDK提供了完整的错误处理机制：

```go
tokenResp, err := client.GetAccessToken(authCode)
if err != nil {
    // 网络错误或解析错误
    log.Printf("请求失败: %v", err)
    return
}

if !tokenResp.Success {
    // API返回的业务错误
    log.Printf("API错误: code=%d, msg=%s", tokenResp.Code, tokenResp.Msg)
    return
}

// 成功处理
fmt.Printf("访问令牌: %s\n", tokenResp.Data.AccessToken)
```

## 配置选项

### 设置超时时间

```go
client.SetTimeout(60 * time.Second) // 设置60秒超时
```

### 克隆客户端

```go
newClient := client.Clone() // 创建客户端副本
```

## 注意事项

1. **令牌过期时间**: 访问令牌通常24小时过期，刷新令牌通常30天过期
2. **错误重试**: 建议在网络错误时实现重试机制
3. **令牌存储**: 建议将令牌安全存储，避免频繁请求
4. **并发安全**: 客户端是并发安全的，可以在多个goroutine中使用

## Gin框架集成

### 创建服务

```go
import "your-module/pkg/xiaohongshu"

// 创建小红书服务
xhsService := xiaohongshu.NewXHSService("your_app_id", "your_secret", false)

// 注册路由
r := gin.Default()
xhsService.RegisterRoutes(r)

// 启动服务
r.Run(":8080")
```

### API端点

- `POST /api/xiaohongshu/oauth/access_token` - 获取访问令牌
- `POST /api/xiaohongshu/oauth/refresh_token` - 刷新访问令牌
- `POST /api/xiaohongshu/report/creative` - 获取创意报表

### 请求示例

```bash
# 获取访问令牌
curl -X POST http://localhost:8080/api/xiaohongshu/oauth/access_token \
  -H "Content-Type: application/json" \
  -d '{"auth_code": "d6a0b18531a2b9599a2c1e2361659c00"}'

# 刷新访问令牌
curl -X POST http://localhost:8080/api/xiaohongshu/oauth/refresh_token \
  -H "Content-Type: application/json" \
  -d '{"refresh_token": "5be1789576f45f90ccfbf4ba16ca4a5b"}'

# 获取创意报表
curl -X POST http://localhost:8080/api/xiaohongshu/report/creative \
  -H "Content-Type: application/json" \
  -d '{
    "access_token": "your_access_token",
    "advertiser_id": 1234567890,
    "start_date": "2024-01-01",
    "end_date": "2024-01-07",
    "time_unit": "DAY",
    "page_num": 1,
    "page_size": 20
  }'
```

## 文件结构

```
pkg/xiaohongshu/
├── README.md                    # 文档
├── config.go                    # 配置结构
├── types.go                     # OAuth相关类型定义
├── report_types.go             # 报表相关类型定义
├── client.go                    # 核心客户端
├── client_test.go              # OAuth功能单元测试
├── report_helper.go            # 报表构建器和便利方法
├── report_test.go              # 报表功能单元测试
├── example.go                   # 基础使用示例
├── usage_example.go            # 高级使用示例（令牌管理器）
├── report_example.go           # 报表使用示例
└── gin_integration_example.go  # Gin框架集成示例
```

## 测试

运行测试：

```bash
go test ./pkg/xiaohongshu -v
```

运行测试并查看覆盖率：

```bash
go test ./pkg/xiaohongshu -v -cover
```

## 许可证

MIT License
