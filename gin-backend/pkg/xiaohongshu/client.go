package xiaohongshu

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

// Client 小红书广告API客户端
type Client struct {
	config *Config
	client *http.Client
}

// NewClient 创建小红书广告API客户端
func NewClient(config *Config) *Client {
	return &Client{
		config: config,
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// GetAccessToken 获取访问令牌
func (c *Client) GetAccessToken(authCode string) (*TokenResponse, error) {
	request := &AccessTokenRequest{
		AppId:    c.config.AppId,
		Secret:   c.config.Secret,
		AuthCode: authCode,
	}

	return c.requestToken(c.config.GetAccessTokenUrl(), request)
}

// RefreshToken 刷新访问令牌
func (c *Client) RefreshToken(refreshToken string) (*TokenResponse, error) {
	request := &RefreshTokenRequest{
		AppId:        c.config.AppId,
		Secret:       c.config.Secret,
		RefreshToken: refreshToken,
	}

	return c.requestToken(c.config.GetRefreshTokenUrl(), request)
}

// requestToken 请求令牌的通用方法
func (c *Client) requestToken(url string, request interface{}) (*TokenResponse, error) {
	// 序列化请求数据
	requestData, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("序列化请求数据失败: %w", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(requestData))
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	resp, err := c.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应内容失败: %w", err)
	}

	// 解析响应
	var tokenResponse TokenResponse
	if err := json.Unmarshal(body, &tokenResponse); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	// 检查响应状态
	if !tokenResponse.Success || tokenResponse.Code != 0 {
		return &tokenResponse, fmt.Errorf("API请求失败: code=%d, msg=%s", tokenResponse.Code, tokenResponse.Msg)
	}

	return &tokenResponse, nil
}

// SetTimeout 设置超时时间
func (c *Client) SetTimeout(timeout time.Duration) {
	c.client.Timeout = timeout
}

// Clone 克隆客户端
func (c *Client) Clone() *Client {
	return &Client{
		config: c.config,
		client: &http.Client{
			Timeout: c.client.Timeout,
		},
	}
}
