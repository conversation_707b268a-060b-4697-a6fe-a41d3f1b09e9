package xiaohongshu

// Config 小红书广告API配置
type Config struct {
	AppId  string // 应用ID
	Secret string // 应用密钥
	IsProd bool   // 是否生产环境
}

// GetBaseUrl 获取API基础地址
func (c *Config) GetBaseUrl() string {
	if c.IsProd {
		return "https://adapi.xiaohongshu.com"
	}
	// 测试环境地址，如果有的话
	return "https://adapi.xiaohongshu.com"
}

// GetAccessTokenUrl 获取access token的URL
func (c *Config) GetAccessTokenUrl() string {
	return c.GetBaseUrl() + "/api/open/oauth2/access_token"
}

// GetRefreshTokenUrl 获取refresh token的URL
func (c *Config) GetRefreshTokenUrl() string {
	return c.GetBaseUrl() + "/api/open/oauth2/refresh_token"
}

// GetCreativeReportUrl 获取创意层级离线报表的URL
func (c *Config) GetCreativeReportUrl() string {
	return c.GetBaseUrl() + "/api/open/jg/data/report/offline/creative"
}
