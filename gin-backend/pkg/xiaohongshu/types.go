package xiaohongshu

// AccessTokenRequest 获取访问令牌请求
type AccessTokenRequest struct {
	AppId    string `json:"app_id"`    // 应用ID
	Secret   string `json:"secret"`    // 应用密钥
	AuthCode string `json:"auth_code"` // 授权码
}

// RefreshTokenRequest 刷新令牌请求
type RefreshTokenRequest struct {
	AppId        string `json:"app_id"`        // 应用ID
	Secret       string `json:"secret"`        // 应用密钥
	RefreshToken string `json:"refresh_token"` // 刷新令牌
}

// Advertiser 广告主信息
type Advertiser struct {
	AdvertiserId   int64  `json:"advertiser_id"`   // 广告主ID
	AdvertiserName string `json:"advertiser_name"` // 广告主名称
}

// TokenData 令牌数据
type TokenData struct {
	UserId                   string       `json:"user_id"`                     // 用户ID
	RoleType                 int          `json:"role_type"`                   // 角色类型
	ApprovalAdvertisers      []Advertiser `json:"approval_advertisers"`        // 授权的广告主列表
	RefreshToken             string       `json:"refresh_token"`               // 刷新令牌
	AdvertiserId             int64        `json:"advertiser_id"`               // 广告主ID
	RefreshTokenExpiresIn    int64        `json:"refresh_token_expires_in"`    // 刷新令牌过期时间(秒)
	ApprovalRoleType         int          `json:"approval_role_type"`          // 授权角色类型
	PlatformType             int          `json:"platform_type"`               // 平台类型
	AccessToken              string       `json:"access_token"`                // 访问令牌
	AccessTokenExpiresIn     int64        `json:"access_token_expires_in"`     // 访问令牌过期时间(秒)
}

// TokenResponse 令牌响应
type TokenResponse struct {
	Code    int        `json:"code"`    // 状态码
	Success bool       `json:"success"` // 是否成功
	Msg     string     `json:"msg"`     // 消息
	Data    *TokenData `json:"data"`    // 数据
}

// ErrorResponse 错误响应
type ErrorResponse struct {
	Code    int    `json:"code"`    // 错误码
	Success bool   `json:"success"` // 是否成功
	Msg     string `json:"msg"`     // 错误消息
}
